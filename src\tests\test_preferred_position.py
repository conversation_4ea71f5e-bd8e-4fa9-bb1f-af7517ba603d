from detections.free_space import EmptyRectArea, sort_to_preference


def test_sort_1_element():
    """
    Test the sort_to_preference function with a single element.
    """
    candidates = [EmptyRectArea(10, 20, 30, 40)]
    sorted_candidates = sort_to_preference(candidates)
    assert sorted_candidates == [(10, 20, 30, 40, 0, 0, 0)], (
        f'Expected [(10, 20, 30, 40, 0, 0, 0)], got {sorted_candidates}'
    )


def test_sort_5_elements_with_max_candidates():
    """
    Test the sort_to_preference function with multiple elements.
    """
    candidates = [
        EmptyRectArea(0, 20, 30, 40),
        EmptyRectArea(0, 60, 30, 40),
        EmptyRectArea(0, 80, 30, 40),
        EmptyRectArea(0, 100, 30, 40),
        EmptyRectArea(0, 120, 30, 40),
    ]
    sorted_candidates = sort_to_preference(candidates, max_candidates=3)
    assert sorted_candidates == [
        (0, 120, 30, 40, 0, 0, 0),
        (0, 100, 30, 40, 0, 0, 0),
        (0, 80, 30, 40, 0, 0, 0),
    ], (
        f'Expected [(0, 120, 30, 40, 0, 0, 0), (0, 100, 30, 40, 0, 0, 0), (0, 80, 30, 40, 0, 0, 0)], got {sorted_candidates}'
    )


def test_sort_elements_same_size():
    """
    Test the sort_to_preference function with multiple elements of the same size.
    """
    candidates = [
        EmptyRectArea(0, 20, 30, 40),
        EmptyRectArea(0, 60, 30, 40),
        EmptyRectArea(0, 80, 30, 40),
    ]
    sorted_candidates = sort_to_preference(candidates)
    assert sorted_candidates == [
        (0, 80, 30, 40, 0, 0, 0),
        (0, 60, 30, 40, 0, 0, 0),
        (0, 20, 30, 40, 0, 0, 0),
    ], (
        f'Expected [(0, 80, 30, 40, 0, 0, 0), (0, 60, 30, 40, 0, 0, 0), (0, 20, 30, 40, 0, 0, 0)], got {sorted_candidates}'
    )


def test_sort_elements_different_sizes():
    candidates = [
        EmptyRectArea(0, 20, 20, 20),
        EmptyRectArea(20, 20, 5, 5),
        EmptyRectArea(40, 20, 100, 20),
    ]
    sorted_candidates = sort_to_preference(candidates)
    assert sorted_candidates == [
        (40, 20, 100, 20, 0, 0, 0),
        (0, 20, 20, 20, 0, 0, 0),
        (20, 20, 5, 5, 0, 0, 0),
    ], (
        f'Expected [(40, 20, 100, 20, 0, 0, 0), (0, 20, 20, 20, 0, 0, 0), (20, 20, 5, 5, 0, 0, 0)], got {sorted_candidates}'
    )
