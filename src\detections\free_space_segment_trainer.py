import json

import cv2 as cv
import numpy as np
import matplotlib.pyplot as plt
from skimage import segmentation, feature, future
from sklearn.ensemble import RandomForestClassifier
from functools import partial

# Load image
y = 242
h = 2000
full_img = cv.imread(
    'data/OCR/20250415/images/images_processed/20250415_102820052292_prep.png',
    cv.IMREAD_GRAYSCALE,
)
img = full_img[y:y + h, :]

# Load labelme labels
# Polygons only
# labels: clear, text
training_labels = np.zeros(img.shape[:2], dtype=np.uint8)
json_data = json.load(open('data/OCR/20250415/images/images_processed/20250415_102820052292_prep.json', 'r'))
shapes = json_data["shapes"]
