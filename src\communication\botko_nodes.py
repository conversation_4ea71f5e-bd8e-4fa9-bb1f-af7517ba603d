from communication.opcua_nodes import OPCUANodes, TypedNode


class BotkoOPCUANode(OPCUANodes):
    """Enum for OPC UA nodes."""

    BOTKO_LIVE_BIT = TypedNode(node_id='ns=2;i=2', data_type=bool)
    BOTKO_FAULTED = TypedNode(node_id='ns=2;i=3', data_type=bool)
    BOTKO_ENGRAVING_BUSY = TypedNode(node_id='ns=2;i=4', data_type=bool)
    BOTKO_SKIPPED_ENGRAVING = TypedNode(node_id='ns=2;i=5', data_type=bool)
    BOTKO_DIAMETER = TypedNode(node_id='ns=2;i=6', data_type=int)
    BOTKO_LENGTH = TypedNode(node_id='ns=2;i=7', data_type=int)
    BOTKO_SCAN_REQUEST = TypedNode(node_id='ns=2;i=8', data_type=bool)

    HV_LIVE_BIT = TypedNode(node_id='ns=2;i=9', data_type=bool)
    HV_FAULTED = TypedNode(node_id='ns=2;i=10', data_type=bool)
    HV_SCAN_ACKNOWLEDGE = TypedNode(node_id='ns=2;i=11', data_type=bool)
    HV_SCAN_BUSY = TypedNode(node_id='ns=2;i=12', data_type=bool)
    HV_SCAN_FINISHED = TypedNode(node_id='ns=2;i=13', data_type=bool)
    HV_START_ROTATION_REQUEST = TypedNode(node_id='ns=2;i=14', data_type=bool)
    HV_ENGRAVING_LOCATION_READY = TypedNode(node_id='ns=2;i=15', data_type=bool)
    HV_PASSPORT_READY = TypedNode(node_id='ns=2;i=16', data_type=bool)

    HV_ENGRAVING_RADIUS = TypedNode(node_id='ns=2;i=17', data_type=int)
    HV_ENGRAVING_HEIGHT = TypedNode(node_id='ns=2;i=18', data_type=int)
    HV_ENGRAVING_ANGLE = TypedNode(node_id='ns=2;i=19', data_type=int)

    HV_PASSPORT_OWNER_NAME = TypedNode(node_id='ns=2;i=20', data_type=str)
    HV_PASSPORT_OWNER_CODE = TypedNode(node_id='ns=2;i=21', data_type=int)
    HV_PASSPORT_SERIAL_NUMBER = TypedNode(node_id='ns=2;i=22', data_type=str)
    HV_PASSPORT_MANUFACTURER_NAME = TypedNode(node_id='ns=2;i=23', data_type=str)
    HV_PASSPORT_MANUFACTURER_CODE = TypedNode(node_id='ns=2;i=24', data_type=int)
    HV_PASSPORT_MANUFACTURER_SERIAL_NUMBER = TypedNode(node_id='ns=2;i=25', data_type=str)
    HV_PASSPORT_DATE_MANUFACTURING = TypedNode(node_id='ns=2;i=26', data_type=str)
    HV_PASSPORT_DATE_LAST_TEST = TypedNode(node_id='ns=2;i=27', data_type=str)
    HV_PASSPORT_TEST_PRESSURE = TypedNode(node_id='ns=2;i=28', data_type=float)
    HV_PASSPORT_CAPACITY = TypedNode(node_id='ns=2;i=29', data_type=float)
    HV_PASSPORT_ORIGINAL_TARRA_WEIGHT = TypedNode(node_id='ns=2;i=30', data_type=float)
